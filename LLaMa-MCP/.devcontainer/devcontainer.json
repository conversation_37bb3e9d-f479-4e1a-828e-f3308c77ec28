{"name": "Python 3 + Node.js", "image": "mcr.microsoft.com/devcontainers/python:1-3.11-bullseye", "features": {"ghcr.io/devcontainers/features/node:1": {"version": "18"}}, "customizations": {"codespaces": {"openFiles": ["README.md", "llama_mcp_streamlit/main.py"]}, "vscode": {"settings": {}, "extensions": ["ms-python.python", "ms-python.vscode-pylance", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint"]}}, "updateContentCommand": "sudo apt update && sudo apt upgrade -y && sudo apt install -y curl git && curl -sSL https://install.python-poetry.org | python3 - && export PATH=\"$HOME/.local/bin:$PATH\" && poetry install && npm install -g npx && echo '✅ Dependencies Installed'", "postAttachCommand": {"server": "streamlit run llama_mcp_streamlit/main.py --server.enableCORS false --server.enableXsrfProtection false"}, "portsAttributes": {"8501": {"label": "Application", "onAutoForward": "openPreview"}}, "forwardPorts": [8501]}