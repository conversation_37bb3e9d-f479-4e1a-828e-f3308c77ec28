# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
env/
.venv/
*.egg-info/
Pipfile
Pipfile.lock

# Streamlit cache and logs
.streamlit/
.history
.db.sqlite3

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# VS Code settings
.vscode/
*.code-workspace

# Environment variables and secrets
.env
.env.local
.env.*.local
!.env.example

# Log files
logs/
*.log

# OS-specific files
.DS_Store
Thumbs.db

# Build artifacts
dist/
build/

# Docker
docker-compose.override.yml
*.dockerfile

# Misc
*.swp
*.swo
*.bak
