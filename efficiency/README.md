# 智能对话反思系统

这是一个基于LLM的对话反思系统，它能够与用户进行日常对话，并通过反思机制总结用户的习惯和特征，从而在未来的对话中提供更个性化的回应。

## 功能特点

- 实时对话：支持与用户进行自然语言对话
- 自由引用：支持用户自由引用历史会话
- 记忆系统：保存用户的重要信息和特征

## 安装步骤

1. 克隆项目到本地

### 使用 python 安装
2. 安装依赖：
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate # Linux/Mac
# 或
.\venv\Scripts\activate # Windows
# 安装依赖
pip install -r requirements.txt

```

### 使用 uv 安装（推荐）
```bash
# 创建虚拟环境
uv venv
# 安装依赖
uv pip install -r requirements.txt
```

### 使用 Docker 安装

```bash
# 构建镜像
docker build -t chat-reflection-system .
# 运行容器
docker run -p 8501:8501 -v $(pwd)/data:/app/data chat-reflection-system
```

## 配置

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入必要的配置：
```bash
# OpenAI API 配置
CHAT_BASE_URL=your_chat_base_url
CHAT_API_KEY=your_chat_api_key
EMBEDDING_BASE_URL=your_embedding_base_url
EMBEDDING_API_KEY=your_embedding_api_key
MEM_BASE_URL=your_memory_base_url
MEM_API_KEY=your_memory_api_key
```

## 运行应用

```bash
streamlit run app.py
# python3 -m streamlit run app.py
# uv run app.py
# 访问 http://localhost:8501 查看应用。
```

## roadmap

- [x] 添加 chat 正常召回以及对话功能
  - [x] 需要记忆能够根据 synonyms 召回
    - [x] 需要测试 memory 的基本功能，增删改查
- [ ] 工具的调用
  - [ ] 模仿 cline 的 prompt， 先接入 function calling 再接入 MCP？
    - [ ] 实现 cline 中的 Tools 函数， 完成 cline 基本功能。
    - [ ] 先用 mcp python sdk 拉起来服务，调通 tools resource prompt 的调用
    - [ ] MCP 支持配置 streamlit 的 配置 UI
  - [ ] 模仿 cline 的 prompt， 写自己的分级 Agent
    - [ ] 一个 Agent 输入是一个任务类型 （召回相关记忆 & 工具） & 计划，输出任务结果
    - [ ] 选择调用 Tools，结果反馈继续进行
  - [ ] 给记忆打标签 & 总结

