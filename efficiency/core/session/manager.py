"""
    __init__.py
"""
import os
import glob
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from .models import Session
from .storage import SessionStorage
from .utils import (
    generate_session_id,
    get_start_of_day_week_month_year,
    calc_consultation_period
)


class SessionManager:
    """"""
    def __init__(self, storage: SessionStorage = None):
        """初始化会话管理器
        
        Args:
            storage: SessionStorage 实例，用于处理会话的存储和加载
        """
        self.storage = storage or SessionStorage()
        self.sessions = self.load_sessions()

    def save(self, session):
        """通过 storage 保存单个会话
        
        Args:
            session: 要保存的会话对象
        """
        # 确保会话有 id
        if not hasattr(session, 'session_id') or not session.session_id:
            session.session_id = str(uuid.uuid4())
        
        # 使用 storage 保存会话
        self.storage.save(session)
        
        # 更新内存中的会话列表
        self.sessions[session.session_id] = session

    def load_sessions(self) -> Dict[str, Session]:
        """从 storage 加载所有会话"""
        return self.storage.load_all()

    def save_all(self):
        """保存所有会话"""
        for session in self.sessions.values():
            self.save(session)

    def get_session(self, session_id: str) -> Optional[Session]:
        """获取指定 ID 的会话"""
        return self.sessions.get(session_id)

    def create_session(self, name: str = None) -> Session:
        """创建新会话"""
        session = Session(name=name)
        self.sessions[session.session_id] = session
        self.save(session)
        return session

    def delete_session(self, session_id: str):
        """删除指定会话"""
        if session_id in self.sessions:
            self.storage.delete_session(session_id)
            del self.sessions[session_id]

    def new(self, control_params: Dict[str, Any]) -> Session:
        """创建新的会话"""
        session_data = {**control_params}
        session_data["session_id"] = generate_session_id()
        session_data["conversation"] = []
        session_data["session_path"] = self.storage.root
        session_data.setdefault("session_name", "Default Session")
        
        session = Session.from_dict(session_data)
        self.storage.save(session)
        self.sessions[session.session_id] = session
        return session
    
    def get(self, control_params: Dict[str, Any]) -> Optional[Session]:
        """获取会话"""
        session_id = control_params.get("session_id")
        session_name = control_params.get("session_name")
        
        if session_id:
            return self.storage.load(session_id)
        elif session_name:
            # 通过session_name查找
            sessions = self._find_by_name(session_name)
            return sessions[0] if sessions else None
        return None
    
    def _find_by_name(self, session_name: str) -> List[Session]:
        """通过名称查找会话"""
        pattern = os.path.join(self.storage.root, "*.json")
        matching_sessions = []
        
        for session_file in glob.glob(pattern):
            session_id = os.path.basename(session_file).split(".")[0]
            session = self.storage.load(session_id)
            if (session and session_name.startswith(session.session_name) 
                and not getattr(session, 'deleted', False)):
                matching_sessions.append(session)
        
        return matching_sessions
    
    def list(self) -> List[Dict[str, Any]]:
        """列出所有会话"""
        pattern = os.path.join(self.storage.root, "*.json")
        files = glob.glob(pattern)
        files.sort(key=os.path.getctime, reverse=True)
        
        today, start_of_week, start_of_month, start_of_year = get_start_of_day_week_month_year()
        sessions = []
        
        for session_file in files:
            session_id = os.path.basename(session_file).split(".")[0]
            session = self.storage.load(session_id)
            if not session:
                continue
                
            create_time = datetime.fromtimestamp(os.path.getctime(session_file))
            consultation_period = calc_consultation_period(
                create_time, today, start_of_week, start_of_month, start_of_year
            )
            
            sessions.append({
                "session_id": session_id,
                "session_name": f"{session.session_name}: {session_id[:4]}",
                "consultation_period": consultation_period,
                "create_time": create_time.strftime('%Y-%m-%d %H:%M')
            })
            
        return sessions 