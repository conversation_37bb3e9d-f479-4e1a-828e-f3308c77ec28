"""
    __init__.py
"""
import os
import json
import threading
from typing import Optional, Dict
from .models import Session


class SessionStorage:
    """"""
    def __init__(self, root_path: str = "./data/sessions"):
        self.root = root_path
        self.locks: Dict[str, threading.Lock] = {}
        os.makedirs(self.root, exist_ok=True)

    def _get_lock(self, session_path: str) -> threading.Lock:
        """获取或创建文件锁"""
        if session_path not in self.locks:
            self.locks[session_path] = threading.Lock()
        return self.locks[session_path]

    def save(self, session: Session) -> None:
        """保存会话到文件"""
        session_path = os.path.join(session.session_path, f"{session.session_id}.json")
        lock = self._get_lock(session_path)
        
        with lock:
            with open(session_path, "w", encoding='utf-8') as f:
                json.dump(session.to_dict(), f, indent=4, ensure_ascii=False)

    def load(self, session_id: str) -> Optional[Session]:
        """从文件加载会话"""
        session_path = os.path.join(self.root, f"{session_id}.json")
        if not os.path.exists(session_path):
            return None
        with open(session_path, encoding='utf-8') as f:
            session_data = json.load(f)
            return Session.from_dict(session_data)
    
    def load_all(self) -> Dict[str, Session]:
        """从文件加载所有会话"""
        sessions = {}
        for file in os.listdir(self.root):
            if file.endswith(".json"):
                session_id = file[:-5]
                session_path = os.path.join(self.root, file)
                with open(session_path, encoding='utf-8') as f:
                    session_data = json.load(f)
                    if session_data.get("deleted"):
                        continue
                    sessions[session_id] = Session.from_dict(session_data)
        return sessions