"""
    __init__.py
"""
from datetime import datetime, timedelta
import uuid


def generate_session_id() -> str:
    """生成一个随机的session_id"""
    return str(uuid.uuid4())


def get_start_of_day_week_month_year():
    """获取今天、本周、本月、本年的开始日期"""
    now = datetime.now()
    today = now.date()
    start_of_week = (now - timedelta(days=now.weekday())).date()
    start_of_month = now.replace(day=1).date()
    start_of_year = now.replace(month=1, day=1).date()
    return today, start_of_week, start_of_month, start_of_year


def calc_consultation_period(create_time: datetime, today, start_of_week, 
                           start_of_month, start_of_year) -> str:
    """计算会话所属时间段"""
    create_date = create_time.date()
    if create_date == today:
        return "Today"
    elif create_date >= start_of_week:
        return "ThisWeek"
    elif create_date >= start_of_month:
        return "ThisMonth"
    elif create_date >= start_of_year:
        return "ThisYear"
    return "Earlier" 