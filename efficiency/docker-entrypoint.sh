#!/bin/bash
set -e

# 确保数据目录存在并具有正确的权限
mkdir -p /app/data
mkdir -p /app/config
mkdir -p /home/<USER>/.npm
mkdir -p /home/<USER>/.npm/_logs
chown -R appuser:appuser /app/data
chown -R appuser:appuser /app/config
chown -R appuser:appuser /home/<USER>/.npm

# 设置npm配置
export NPM_CONFIG_CACHE=/home/<USER>/.npm
export NPM_CONFIG_PREFIX=/home/<USER>/.npm-global
mkdir -p /home/<USER>/.npm-global
chown -R appuser:appuser /home/<USER>/.npm-global

# 以appuser用户身份执行命令
exec gosu appuser "$@"
