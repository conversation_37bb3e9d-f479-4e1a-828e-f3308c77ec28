"""
    __init__.py
"""
import os
import pandas as pd
import streamlit as st
import re
import subprocess
from typing import Optional, Dict, Any, Union, Tuple
from pathlib import Path
from utils.format_conversion import list_to_markdown


def execute_command(command: str, requires_approval: str = "false") -> Tuple[bool, str]:
    """
    Execute a CLI command on the system.
    
    Args:
        command: The CLI command to execute
        requires_approval: Whether this command requires explicit user approval
        
    Returns:
        Dict containing status and output/error information
    """
    if requires_approval.lower() == "false":
        requires_approval = False
    else:
        requires_approval = True

    success, result = False, ""

    if requires_approval:
        confirmation = st.text_input(f"Do you want to execute: {command}? [y/N] ")
        if confirmation.lower() != 'y':
            result = "Command execution cancelled by user"
            return success, result

    try:
        process = subprocess.run(command, shell=True, check=True,
                                 capture_output=True, text=True)
        success = True
        result = process.stdout
        return success, result
    except subprocess.CalledProcessError as e:
        result = f"Command execution failed: {e}"
        return success, result


def read_file(path: str) -> <PERSON>ple[bool, str]:
    """
    Read the contents of a file at the specified path.
    
    Args:
        path: The path of the file to read
        
    Returns:
        Dict containing status and content/error information
    """
    success, result = False, ""

    try:
        with open(path, 'r', encoding='utf-8') as f:
            result = f.read()
            success = True
            return success, result
    except Exception as e:
        result = f"Error reading file {path}: {e}"
        return success, result


def read_file_structure(file_path, sheet_name=0, start_row=0) -> Tuple[bool, str]:
    """
    读取CSV或Excel文件的表结构，包括列名和数据类型。

    参数：
    file_path (str): 文件的路径，支持CSV和Excel文件。
    sheet_name (str/int): 如果是Excel文件，需要读取的表名或表索引，默认为第一个表。
    start_row (int): 从第几行开始读取数据（从0计数），默认为0。

    返回：
    Tuple[bool, str]: (是否成功, markdown格式的表结构信息)
    """
    success, result = False, ""
    try:
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path, skiprows=start_row)
        elif file_path.endswith(('.xls', '.xlsx')):
            df = pd.read_excel(file_path, sheet_name=sheet_name, skiprows=start_row)
        else:
            result = "File type not supported"
            return success, result

        # 构建字典，键为列名，值为数据类型，TODO：result 转换为 markdown 的表格
        result_dict = {col: str(dtype) for col, dtype in zip(df.columns, df.dtypes)}
        markdown_lines = ["| Key | Value |", "| --- | ----- |"]
        for key, value in result_dict.items():
            markdown_lines.append(f"| {key} | {value} |")
        result = "\n".join(markdown_lines)

        success = True
        return success, result

    except Exception as e:
        result = f"Error reading file {file_path}: {e}"
        raise e
        return success, result


def write_to_file(path: str, content: str) -> Tuple[bool, str]:
    """
    Write content to a file at the specified path.
    
    Args:
        path: The path of the file to write to
        content: The complete content to write to the file
        
    Returns:
        Dict containing status and error information
    """
    success, result = False, ""

    try:
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'w', encoding='utf-8') as f:
            f.write(content)
        success = True
        return success, result
    except Exception as e:
        result = f"Error writing to file {path}: {e}"
        return success, result


def replace_in_file(path: str, diff: str) -> Tuple[bool, str]:
    """
    Replace sections of content in an existing file using SEARCH/REPLACE blocks.
    
    Args:
        path: The path of the file to modify
        diff: One or more SEARCH/REPLACE blocks defining the changes
        
    Returns:
        Dict containing status and error information
    """
    success, result = False, ""

    # 检查 diff 格式的基本有效性
    if 'SEARCH\n' not in diff or 'REPLACE\n' not in diff:
        result = "Invalid diff block format"
        return success, result

    success, read_result = read_file(path)
    if not success:
        return success, read_result

    content = read_result["content"]
    blocks = diff.split('SEARCH\n')

    try:
        for block in blocks[1:]:
            # 确保 'REPLACE\n' 存在以安全拆分
            if 'REPLACE\n' in block:
                search_content, replace_part = block.split('\nREPLACE\n', 1)
                replace_content = replace_part.strip()
                new_content = content.replace(search_content, replace_content)
                if new_content != content:
                    content = new_content
                    success = True

        success, write_result = write_to_file(path, content)
        if not success:
            return success, write_result

        return success, result
    except Exception as e:
        # 捕获所有异常并记录错误
        result = f"发生错误: {str(e)}"
        return success, result


def search_files(path: str, regex: str, file_pattern: Optional[str] = None) -> Tuple[bool, str]:
    """
    Perform a regex search across files in a specified directory.
    
    Args:
        path: The directory path to search in
        regex: The regular expression pattern to search for
        file_pattern: Optional glob pattern to filter files
        
    Returns:
        Dict containing status, matches and error information
    """
    success, result = False, ""

    try:
        pattern = re.compile(regex)
        path_obj = Path(path)

        if file_pattern:
            files = path_obj.rglob(file_pattern)
        else:
            files = path_obj.rglob('*')

        for file in files:
            if file.is_file():
                success, read_result = read_file(str(file))
                if success:
                    content = read_result
                    matches = pattern.finditer(content)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        context = content[max(0, match.start() - 50):match.end() + 50]
                        result += f"- file: {str(file)}, line: {line_num}, context: {context}, match: {match.group()}\n"

        success = True
        return success, result
    except Exception as e:
        result = f"Error during search: {e}"
        return success, result


def list_files(path: str, recursive: Optional[bool] = False) -> Tuple[bool, str]:
    """
    List files and directories within the specified directory.
    
    Args:
        path: The directory path to list contents for
        recursive: Whether to list files recursively
        
    Returns:
        Dict containing status, files list and error information
    """
    success, result = False, ""

    try:
        path_obj = Path(path)
        if recursive:
            result = [str(p) for p in path_obj.rglob('*')]
        else:
            result = [str(p) for p in path_obj.iterdir()]
        success = True
        return success, result
    except Exception as e:
        result = f"Error listing files: {e}"
        return success, result


def list_code_definition_names(path: str) -> Tuple[bool, str]:
    """
    List definition names used in source code files.
    
    Args:
        path: The directory path to analyze
        
    Returns:
        Dict containing status, definitions list and error information
    """
    success, result = False, ""

    try:
        def_pattern = re.compile(r'(?:def|class)\s+([a-zA-Z_][a-zA-Z0-9_]*)')

        for file in Path(path).rglob('*.py'):
            success, read_result = read_file(str(file))
            if success:
                matches = def_pattern.finditer(read_result)
                for match in matches:
                    result += f"- name: {match.group(1)}, file: {str(file)}\n"

        success = True
        return success, result
    except Exception as e:
        result = f"Error analyzing code: {e}"
        return success, result


def use_mcp_tool(server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, str]:
    """
    Use a tool provided by a connected MCP server.
    
    Args:
        server_name: The name of the MCP server
        tool_name: The name of the tool to execute
        arguments: JSON object containing the tool's input parameters
        
    Returns:
        Dict containing status, response data and error information
    """
    success, result = False, ""

    try:
        # Mock implementation
        success = True
        result = {
            "tool": tool_name,
            "server": server_name,
            "arguments": arguments,
            "response": "Mock response"
        }
        return success, result
    except Exception as e:
        result = f"Error using MCP tool: {e}"
        return success, result


def access_mcp_resource(server_name: str, uri: str) -> Tuple[bool, str]:
    """
    Access a resource provided by a connected MCP server.
    
    Args:
        server_name: The name of the MCP server
        uri: The URI identifying the specific resource
        
    Returns:
        Dict containing status, resource data and error information
    """
    success, result = False, ""

    try:
        # Mock implementation
        success = True
        result = {
            "server": server_name,
            "uri": uri,
            "data": "Mock resource data"
        }
        return success, result
    except Exception as e:
        result = f"Error accessing MCP resource: {e}"
        return success, result


def ask_followup_question(question: str) -> Tuple[bool, str]:
    """
    Ask the user a followup question.
    
    Args:
        question: The question to ask the user
        
    Returns:
        Dict containing status, response and error information
    """
    success, result = False, ""

    try:
        result = input(f"{question}\nYour response: ")
        success = True
        return success, result
    except Exception as e:
        result = f"Error getting user input: {e}"
        return success, result


def attempt_completion(result: str, command: Optional[str] = None) -> Tuple[bool, str]:
    """
    Present the result of work to the user.
    
    Args:
        result: The result of the task
        command: Optional CLI command to demonstrate the result
        
    Returns:
        Dict containing status and presentation information
    """
    success, result = False, ""

    print("\nTask Result:")
    print(result)
    if command:
        print(f"\nDemo command: {command}")

    return success, result


def list_files_in_directory(directory_path):
    """
    列举指定路径下的所有文件
    Args:
        directory_path:

    Returns:

    """
    # 用于存储所有文件的绝对路径
    file_paths = []

    # 遍历指定目录及其子目录
    for root, _, files in os.walk(directory_path):
        for file in files:
            # 获取文件的绝对路径
            file_path = os.path.abspath(os.path.join(root, file))
            file_paths.append(file_path)

    # 以 Markdown 格式打印文件路径
    markdown_output = "\n".join([f"- `{path}`" for path in file_paths])
    return markdown_output
