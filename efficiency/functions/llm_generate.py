# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/17 4:04 下午
@Auth ： Jinx
@File ：llm_generate.py
@IDE ：PyCharm
"""
import os
from mysql_interperter import SQLConnection


def to_know_table(table_name: str) -> tuple[bool, str]:
    """"""
    user = os.getenv("MYSQL_USER")
    password = os.getenv("MYSQL_PASSWORD")
    port = os.getenv("MYSQL_PORT")
    host = os.getenv("MYSQL_HOST")
    db = os.getenv("MYSQL_DB")
    try:
        mysql_conn = SQLConnection(
            # f"mysql+pymysql://{user}:{password}@{host}:{port}/{db}",
            "mysql+pymysql://root:12345678@localhost:3306/test_db",
            echo=False
        )
        # 1. 针对每个表，获取总数、获取每个字段的名字及其comment、获取枚举值，可以均匀采样 N 条。
        count = mysql_conn.execute_query(f"SELECT COUNT(*) FROM {table_name}")[0]['COUNT(*)']
        # 获取每个字段的名字及其comment
        fields = mysql_conn.execute_query(
            f"SHOW COLUMNS FROM {table_name}")
        columns = [item['Field'] for item in fields]
        columns_dict = {}
        for field in fields:
            res = mysql_conn.execute_query(
                f"SELECT DISTINCT({field['Field']}) FROM {table_name}")
            value_list = [item[field['Field']] for item in res]
            value_count = len(list(set(value_list)))
            print(value_count)
            if value_count <= 50:
                value = value_list
            elif abs(value_count - count) <= count * 0.4:
                value = list(set(value_list))[::int(value_count / 50)]
            else:
                value = "数值太多无法枚举"
            columns_dict[field['Field']] = value
        prompt = construct_prompt(table_name, count, columns, columns_dict)
        print(prompt)
        return True, prompt
    except Exception as e:
        print(e)
        return False, str(e)


def construct_prompt(table_name: str, count: int, columns: list, columns_dict: dict) -> str:
    """
    构建表结构描述的提示词（每个字段信息后都有换行符）

    参数:
        table_name: 表名
        count: 表记录总数
        columns: 字段名列表
        columns_dict: 字段枚举值字典 {字段名: 枚举值列表}
    """
    # 处理字段详细信息，每个字段后加换行符
    fields_details = '\n            '.join(
        [f'字段名: {k}: 枚举值: {v if v is not None else "数值太多无法枚举"}\n'
         for k, v in columns_dict.items()]
    )

    return f"""
        ### 基本信息\n
            - 表名: {table_name}\n
            - 记录总数: {count:,} 条\n
            - 字段数量: {len(columns)} 个\n
            - 字段有: {', '.join(columns)}\n
            - 字段详细信息:\n
                    {fields_details}
        """.strip()


if __name__ == '__main__':
    to_know_table("Interface_labellist_table")
