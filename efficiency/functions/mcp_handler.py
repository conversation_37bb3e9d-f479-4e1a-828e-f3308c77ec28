#!/usr/bin/env python3
"""
MCP工具调用处理器 - 处理<use_mcp_tool>标签
"""
import sys
import json
import re
from typing import Dict, Any, Tuple

# 添加项目根目录到Python路径
sys.path.append('/app')

try:
    from .universal_mcp import universal_mcp
except ImportError:
    from universal_mcp import universal_mcp

def use_mcp_tool(server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, str]:
    """
    处理MCP工具调用

    Args:
        server_name: MCP服务名称
        tool_name: 工具名称
        arguments: 工具参数

    Returns:
        (是否成功, 结果内容或错误信息)
    """
    return universal_mcp.use_mcp_tool(server_name, tool_name, arguments)

def parse_mcp_tool_call(text: str) -> Dict[str, Any]:
    """
    解析<use_mcp_tool>标签

    Args:
        text: 包含MCP工具调用标签的文本

    Returns:
        解析结果字典
    """
    # 匹配<use_mcp_tool>标签
    pattern = r'<use_mcp_tool>\s*<server_name>(.*?)</server_name>\s*<tool_name>(.*?)</tool_name>\s*<arguments>(.*?)</arguments>\s*</use_mcp_tool>'

    match = re.search(pattern, text, re.DOTALL)

    if not match:
        return {
            "success": False,
            "error": "未找到有效的<use_mcp_tool>标签"
        }

    server_name = match.group(1).strip()
    tool_name = match.group(2).strip()
    arguments_str = match.group(3).strip()

    try:
        # 解析参数JSON
        arguments = json.loads(arguments_str)
    except json.JSONDecodeError as e:
        return {
            "success": False,
            "error": f"参数JSON格式错误: {e}"
        }

    return {
        "success": True,
        "server_name": server_name,
        "tool_name": tool_name,
        "arguments": arguments
    }

def handle_mcp_tool_call(text: str) -> str:
    """
    处理包含MCP工具调用的文本

    Args:
        text: 包含<use_mcp_tool>标签的文本

    Returns:
        处理结果文本
    """
    # 解析MCP工具调用
    parse_result = parse_mcp_tool_call(text)

    if not parse_result["success"]:
        return f"❌ MCP工具调用解析失败: {parse_result['error']}"

    server_name = parse_result["server_name"]
    tool_name = parse_result["tool_name"]
    arguments = parse_result["arguments"]

    # 调用MCP工具
    success, result = use_mcp_tool(server_name, tool_name, arguments)

    if success:
        return f"✅ MCP工具调用成功\n\n**调用信息:**\n- 服务: {server_name}\n- 工具: {tool_name}\n- 参数: {json.dumps(arguments, ensure_ascii=False)}\n\n**结果:**\n{result}"
    else:
        return f"❌ MCP工具调用失败\n\n**调用信息:**\n- 服务: {server_name}\n- 工具: {tool_name}\n- 参数: {json.dumps(arguments, ensure_ascii=False)}\n\n**错误:**\n{result}"

# 兼容性函数 - 支持旧的调用方式
def query_trains(from_city: str, to_city: str, date: str = "tomorrow") -> str:
    """
    查询火车票的便捷函数

    Args:
        from_city: 出发城市
        to_city: 到达城市
        date: 日期（默认明天）

    Returns:
        查询结果
    """
    arguments = {
        "fromCity": from_city,
        "toCity": to_city,
        "date": date
    }

    success, result = use_mcp_tool("chinarailway", "search", arguments)

    if success:
        return f"🚄 **火车票查询结果: {from_city} → {to_city} ({date})**\n\n{result}"
    else:
        return f"❌ **火车票查询失败: {from_city} → {to_city} ({date})**\n\n{result}"

# 示例用法
if __name__ == "__main__":
    # 测试解析
    test_text = """
    <use_mcp_tool>
    <server_name>chinarailway</server_name>
    <tool_name>search</tool_name>
    <arguments>
    {
        "fromCity": "上海",
        "toCity": "北京",
        "date": "tomorrow"
    }
    </arguments>
    </use_mcp_tool>
    """

    result = handle_mcp_tool_call(test_text)
    print(result)
