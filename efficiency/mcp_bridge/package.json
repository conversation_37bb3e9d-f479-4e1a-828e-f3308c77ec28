{"name": "efficiency-mcp-bridge", "version": "1.0.0", "description": "MCP桥接服务 - 连接Python应用和Smithery MCP服务", "type": "module", "main": "mcp_bridge.js", "scripts": {"start": "node mcp_bridge.js", "dev": "node --watch mcp_bridge.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "@smithery/sdk": "^1.0.0", "express": "^4.18.2", "cors": "^2.8.5"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "smithery", "bridge", "efficiency"], "author": "Efficiency Team", "license": "MIT"}