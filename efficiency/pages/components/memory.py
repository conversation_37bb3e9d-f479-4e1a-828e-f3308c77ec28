"""
    memory.py
"""
# Streamlit UI
import streamlit as st
import os


@st.dialog("Change Memory Path")
def change_memory_path_dialog():
    """"""
    selected_path = st.text_input("请输入文件夹路径:", value=st.session_state.home_dir)

    if st.button("确认"):
        if selected_path and os.path.isdir(selected_path):
            st.session_state.home_dir = selected_path
            st.session_state.memory_manager.load_memory(st.session_state.home_dir)
            st.session_state.memories = st.session_state.memory_manager._memory_data[::-1][:10]
            st.success("路径已成功更改！")
            
            st.rerun()
        else:
            st.error("无效的文件夹路径")


@st.dialog("Are you sure you want to save all modifications？")
def save_all_change_dialog():
    """"""
    if st.button("确认"):
        st.session_state.memory_manager.save_memory()
        st.rerun()


@st.dialog("Add your Memory")
def add_memory_dialog():
    """"""
    memory_text = st.text_area("Memory Text", key="memory_text")
    memory_summary = st.text_area("Summary", key="memory_summary")
    
    # 获取所有标签选项，使用 label 名称作为显示
    labels = st.session_state.memory_manager.list_labels()
    label_options = {label['name']: label['id'] for label in labels}
    
    # 初始化selected_labels，如果session_state中有memory_labels则使用它
    if 'memory_labels' in st.session_state:
        selected_labels = st.session_state.memory_labels
    else:
        selected_labels = []
    
    # 使用multiselect显示标签，并绑定到selected_labels
    selected_labels = st.multiselect(
        "Select Labels", 
        options=list(label_options.keys()), 
        default=selected_labels,
        key="memory_labels"
    )
    # 显示当前路径（放在文件上传器之后，确保用户可以看到当前选择的路径）
    base_path = st.text_input("当前记忆存储根路径", value=st.session_state.home_dir, key="path_display")
    
    # 根据是否选择了标签来修改保存路径
    if not selected_labels:
        # 如果没有选择标签，在路径后追加"default"
        save_path = os.path.join(base_path, '')
    else:
        # 如果选择了标签，在路径后加上第一个标签的路径
        # 使用第一个标签作为路径的一部分
        first_label = selected_labels[0]
        save_path = os.path.join(base_path, first_label)
        # save_path = os.path.join(save_path, first_label+'.md')
    
    # 显示最终的保存路径
    st.text("最终保存路径: " + save_path)
    
    # 转换选中的标签名称为ID
    # selected_label_ids = [label_options[label] for label in selected_labels]
    
    # 创建两列布局，一列放提交按钮，一列放建议按钮
    col1, col2 = st.columns(2)
    
    with col1:
        submit_button = st.button(label='Submit Memory', key="submit_memory")
        if submit_button:
            st.session_state.memory_manager.add_memory(
                memory_text=memory_text,
                summary=memory_summary,
                labels=selected_labels,
                save_path=save_path
            )
            st.success("Memory added successfully!")
            st.rerun()
    
    with col2:
        suggest_button = st.button(label='Suggest Label', key="suggest_button")
        if suggest_button and memory_text:
            # 这里可以添加AI建议标签的逻辑
            # 简单示例：根据文本内容生成一些建议标签
            suggested_labels = suggest_labels_from_text(memory_text)
            
            # 更新选中的标签
            st.session_state.memory_labels = suggested_labels
            
            # 更新保存路径
            if suggested_labels:
                first_label = suggested_labels[0]
                save_path = os.path.join(base_path, first_label)
            
            st.success("已生成建议标签和路径！")
            # 不重新运行应用，而是更新当前对话框中的值
            # 通过更新session_state，下一次渲染时会使用新的值
            st.rerun()


@st.dialog("Update Memory")
def update_memory_dialog(memory_id, memory_manager):
    """"""
    mem = None
    for m in memory_manager._memory_data:
        if m.id == memory_id:
            mem = m
            break
            
    if mem is None:
        st.error(f"Memory {memory_id} not found")
        return
 
    new_text = st.text_area("New Text", value=mem.original_text, key="new_text")
    new_summary = st.text_area("New Summary", value=mem.summary, key="new_summary")
    new_metadata = st.text_area("New Metadata", value=mem.metadata, key="new_metadata", help="Enter metadata as JSON")

    # 标签选择
    label_options = {label['name']: label['id'] for label in memory_manager.list_labels()}
    new_labels = st.multiselect(
        "New Labels",
        options=list(label_options.keys()),
        default=mem.labels,
        key="new_labels"
    )
    # new_label_ids = [label_options[label] for label in new_labels]
    
    # 显示当前路径（放在文件上传器之后，确保用户可以看到当前选择的路径）
    base_path = st.text_input("当前记忆存储根路径", value=st.session_state.home_dir, key="path_display")
    
    # 根据是否选择了标签来修改保存路径
    if not new_labels:
        # 如果没有选择标签，在路径后追加"default"
        save_path = os.path.join(base_path, '')
    else:
        # 如果选择了标签，在路径后加上第一个标签的路径
        # 使用第一个标签作为路径的一部分
        first_label = new_labels[0]
        save_path = os.path.join(base_path, first_label)
        # save_path = os.path.join(save_path, first_label+'.md')
    
    # 显示最终的保存路径
    st.text("最终保存路径: " + save_path)

    if st.button("Update Memory", key="update_memory"):
        memory_manager.update_memory(
            memory_id=mem.id,
            new_text=new_text,
            new_summary=new_summary,
            new_labels=new_labels,
            new_metadata=new_metadata,
            save_path=save_path
        )
        st.success(f"Memory {mem.id} updated")
        st.rerun()


def display_memories(memories, memory_manager):
    """"""
    if not memories:
        st.write("No memories found")
        return
    
    # 创建标签ID到名称的映射
    # label_map = {label['id']: label['name'] for label in memory_manager.list_labels()}
    print(memories)
    for mem in memories:
        print("-----")
        print(mem)
        if not mem:
            continue
        col1, col2, col3 = st.columns([6, 1, 1])
        with col1:
            with st.container():
                col4, col5 = st.columns([1, 1])
                with col4:
                    st.write("Created")
                    st.write(mem.created_at)
                with col5:
                    st.write("Updated")
                    st.write(mem.updated_at)
 
            st.markdown(mem.summary)
            with st.expander("Show Original"):
                st.markdown(mem.original_text)
            st.markdown(f"URI: `{mem.uri}`")
                
        with col2:
            st.write("Labels")
            for label in mem.labels:
                st.write(f"`{label}`")
                
        with col3:
            st.write("Actions")
            if st.button(f"Delete Memory {mem.id}", key=f"delete_memory_{mem.id}"):
                memory_manager.delete_memory(mem.id)
                st.success(f"Memory {mem.id} deleted")
                st.rerun()
            if st.button(f"Update Memory {mem.id}", key=f"update_memory_{mem.id}"):
                update_memory_dialog(mem.id, memory_manager)
 
        st.markdown("---")