#!/usr/bin/env python3
"""
网络配置和诊断脚本
"""
import os
import ssl
import socket
import asyncio
import aiohttp
import logging
from typing import Dict, Any

def configure_ssl_context():
    """配置SSL上下文以支持HTTPS连接"""
    # 创建默认SSL上下文
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    return ssl_context

def configure_network_environment():
    """配置网络环境变量"""
    # 设置代理（如果需要）
    proxy_settings = {
        'HTTP_PROXY': os.getenv('HTTP_PROXY', ''),
        'HTTPS_PROXY': os.getenv('HTTPS_PROXY', ''),
        'NO_PROXY': os.getenv('NO_PROXY', 'localhost,127.0.0.1'),
    }
    
    for key, value in proxy_settings.items():
        if value:
            os.environ[key] = value
            os.environ[key.lower()] = value

async def test_network_connectivity():
    """测试网络连接"""
    test_urls = [
        'https://google.com',
        'https://github.com',
        'https://registry.smithery.ai',
        'https://server.smithery.ai'
    ]
    
    results = {}
    
    # 配置aiohttp连接器
    connector = aiohttp.TCPConnector(
        ssl=configure_ssl_context(),
        limit=10,
        limit_per_host=5,
        ttl_dns_cache=300,
        use_dns_cache=True,
    )
    
    timeout = aiohttp.ClientTimeout(total=10, connect=5)
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout
    ) as session:
        for url in test_urls:
            try:
                async with session.get(url) as response:
                    results[url] = {
                        'status': response.status,
                        'success': True,
                        'error': None
                    }
            except Exception as e:
                results[url] = {
                    'status': None,
                    'success': False,
                    'error': str(e)
                }
    
    return results

def diagnose_dns():
    """诊断DNS解析"""
    test_domains = [
        'google.com',
        'github.com',
        'registry.smithery.ai',
        'server.smithery.ai'
    ]
    
    results = {}
    for domain in test_domains:
        try:
            ip = socket.gethostbyname(domain)
            results[domain] = {'ip': ip, 'success': True, 'error': None}
        except Exception as e:
            results[domain] = {'ip': None, 'success': False, 'error': str(e)}
    
    return results

async def test_smithery_api(api_key: str):
    """测试Smithery API连接"""
    # 配置网络环境
    configure_network_environment()
    
    # 创建优化的HTTP客户端
    connector = aiohttp.TCPConnector(
        ssl=configure_ssl_context(),
        limit=10,
        limit_per_host=5,
        ttl_dns_cache=300,
        use_dns_cache=True,
        enable_cleanup_closed=True
    )
    
    timeout = aiohttp.ClientTimeout(total=30, connect=10)
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout
    ) as session:
        # 测试Registry API
        try:
            registry_url = 'https://registry.smithery.ai/servers/@nickclyde/duckduckgo-mcp-server'
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Accept': 'application/json',
                'User-Agent': 'efficiency-mcp-client/1.0.0'
            }
            
            async with session.get(registry_url, headers=headers) as response:
                registry_result = {
                    'status': response.status,
                    'success': response.status == 200,
                    'data': await response.text() if response.status == 200 else None,
                    'error': await response.text() if response.status != 200 else None
                }
        except Exception as e:
            registry_result = {
                'status': None,
                'success': False,
                'data': None,
                'error': str(e)
            }
        
        # 测试MCP服务器连接
        try:
            mcp_url = 'https://server.smithery.ai/@nickclyde/duckduckgo-mcp-server/mcp'
            params = {'api_key': api_key}
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream',
                'User-Agent': 'efficiency-mcp-client/1.0.0'
            }
            
            payload = {
                'jsonrpc': '2.0',
                'id': 1,
                'method': 'initialize',
                'params': {
                    'protocolVersion': '2024-11-05',
                    'capabilities': {},
                    'clientInfo': {
                        'name': 'efficiency-mcp-client',
                        'version': '1.0.0'
                    }
                }
            }
            
            async with session.post(mcp_url, params=params, json=payload, headers=headers) as response:
                mcp_result = {
                    'status': response.status,
                    'success': response.status == 200,
                    'data': await response.text() if response.status == 200 else None,
                    'error': await response.text() if response.status != 200 else None
                }
        except Exception as e:
            mcp_result = {
                'status': None,
                'success': False,
                'data': None,
                'error': str(e)
            }
    
    return {
        'registry_api': registry_result,
        'mcp_server': mcp_result
    }

if __name__ == '__main__':
    import json
    
    print("=== 网络诊断开始 ===")
    
    # DNS诊断
    print("\n1. DNS解析测试:")
    dns_results = diagnose_dns()
    for domain, result in dns_results.items():
        status = "✅" if result['success'] else "❌"
        print(f"  {status} {domain}: {result.get('ip', result.get('error'))}")
    
    # 网络连接测试
    print("\n2. 网络连接测试:")
    connectivity_results = asyncio.run(test_network_connectivity())
    for url, result in connectivity_results.items():
        status = "✅" if result['success'] else "❌"
        print(f"  {status} {url}: {result.get('status', result.get('error'))}")
    
    # Smithery API测试
    print("\n3. Smithery API测试:")
    api_key = '70032a0a-cd54-44d0-9f88-19d8afc747aa'
    smithery_results = asyncio.run(test_smithery_api(api_key))
    
    for test_name, result in smithery_results.items():
        status = "✅" if result['success'] else "❌"
        print(f"  {status} {test_name}: 状态码 {result.get('status', 'N/A')}")
        if not result['success'] and result.get('error'):
            print(f"    错误: {result['error'][:200]}...")
