"""
格式转化工具
"""


def list_to_markdown(data_list):
    """
    将列表转化为markdown格式
    Args:
        data_list:

    Returns:

    """
    if not data_list:
        return ""

    # 获取列名
    headers = data_list[0].keys()

    # 创建 Markdown 表头
    markdown = "| " + " | ".join(headers) + " |\n"
    markdown += "| " + " | ".join(["---"] * len(headers)) + " |\n"

    # 添加每一行数据
    for item in data_list:
        row = [str(item.get(header, "")) if item.get(header) is not None else "" for header in headers]
        markdown += "| " + " | ".join(row) + " |\n"

    return markdown
